<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* home/partnership.html.twig */
class __TwigTemplate_25d780d994764e88295496dafe44e85b extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'body' => [$this, 'block_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "home/partnership.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "home/partnership.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Partnership Program - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 6
        yield "    <!-- Section 1: Capitol Academy Partnership Program -->
    <section style=\"background: url('";
        // line 7
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/backgrounds/Banner CA Partners Prog.png"), "html", null, true);
        yield "') center/cover; height: 100vh; padding: 0; margin: 0; display: flex; align-items: center;\">
        <div class=\"container h-100\">
            <div class=\"row align-items-center h-100\">
                <div class=\"col-lg-10\">
                    <!-- Larger Glassmorphism Container -->
                    <div class=\"hero-content text-white p-5 rounded-4\" style=\"background: rgba(255, 255, 255, 0.15); backdrop-filter: blur(20px); border: 1px solid rgba(255, 255, 255, 0.25); width: 85%; margin-top: 140px;\">
                        <h1 class=\"display-4 mb-3\" style=\"font-family: 'Montserrat', sans-serif;\">Capitol Academy Partnership Program</h1>
                        <h2 class=\"h2 fw-bold mb-4\" style=\"font-family: 'Montserrat', sans-serif;\">Earn Money by Promoting Us</h2>
                        <p class=\"mb-5\" style=\"font-size: 1.1rem; line-height: 1.6; font-family: 'Calibri', Arial, sans-serif;\">
                            Give your customers the perfect environment to make better decisions in the financial markets.
                        </p>
                        <div class=\"d-flex justify-content-start\">
                            <a href=\"#benefits\" class=\"btn btn-lg px-5 py-3\" style=\"background: #28a745; border: 2px solid white; border-radius: 8px; font-weight: 600; color: white; transition: all 0.3s ease;\"
                               onmouseover=\"this.style.background='#218838'; this.style.transform='translateY(-2px)'\"
                               onmouseout=\"this.style.background='#28a745'; this.style.transform='translateY(0)'\">
                                Join Now
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section 2: Why Partner with Capitol Academy -->
    <section id=\"benefits\" style=\"background: url('";
        // line 32
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/backgrounds/Bg Why partner with CA.png"), "html", null, true);
        yield "') center/cover; height: 100vh; margin: 0; padding: 0; display: flex; align-items: center;\">
        <div class=\"container h-100\">
            <div class=\"row h-100 align-items-center\">
                <div class=\"col-12\">
                    <div class=\"text-center mb-5\">
                        <h2 class=\"h1 fw-bold mb-4\" style=\"color: #011a2d; font-family: 'Montserrat', sans-serif;\">Why Partner with Capitol Academy ?</h2>
                    </div>
                    <div class=\"row g-5 justify-content-center\">
                        <!-- Financial Benefits -->
                        <div class=\"col-lg-3 col-md-4\">
                            <div class=\"card border-0 shadow-sm\" style=\"background: linear-gradient(to bottom, #0a1f3c, #2a4d7a); transition: transform 0.3s ease, box-shadow 0.3s ease; min-height: 350px;\">
                                <div class=\"card-body text-center p-5\">
                                    <div class=\"benefit-icon-container mb-4\">
                                        <img src=\"";
        // line 45
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/misc/Financial_benefits icon.png"), "html", null, true);
        yield "\" alt=\"Financial Benefits\"
                                             class=\"img-fluid\" style=\"max-height: 80px; width: auto;\"
                                             onerror=\"this.style.display='none'; this.nextElementSibling.style.display='flex';\">
                                    </div>
                                    <h4 class=\"fw-bold mb-3 text-white\" style=\"font-family: 'Montserrat', sans-serif;\">Financial Benefits</h4>
                                    <p class=\"text-white\" style=\"line-height: 1.6; font-family: 'Calibri', Arial, sans-serif;\">
                                        Custom payouts for High-Value Partners & earn CPA from deposits as low as \$1
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Going Global -->
                        <div class=\"col-lg-3 col-md-4\">
                            <div class=\"card border-0 shadow-sm\" style=\"background: linear-gradient(to bottom, #0a1f3c, #2a4d7a); transition: transform 0.3s ease, box-shadow 0.3s ease; min-height: 350px;\">
                                <div class=\"card-body text-center p-5\">
                                    <div class=\"benefit-icon-container mb-4\">
                                        <img src=\"";
        // line 62
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/misc/Going_global.png"), "html", null, true);
        yield "\" alt=\"Going Global\"
                                             class=\"img-fluid\" style=\"max-height: 80px; width: auto;\"
                                             onerror=\"this.style.display='none'; this.nextElementSibling.style.display='flex';\">
                                    </div>
                                    <h4 class=\"fw-bold mb-3 text-white\" style=\"font-family: 'Montserrat', sans-serif;\">Going Global</h4>
                                    <p class=\"text-white\" style=\"line-height: 1.6; font-family: 'Calibri', Arial, sans-serif;\">
                                        Benefit from high-converting products and earn from multiple traffic sources.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Trust & Confidence -->
                        <div class=\"col-lg-3 col-md-4\">
                            <div class=\"card border-0 shadow-sm\" style=\"background: linear-gradient(to bottom, #0a1f3c, #2a4d7a); transition: transform 0.3s ease, box-shadow 0.3s ease; min-height: 350px;\">
                                <div class=\"card-body text-center p-5\">
                                    <div class=\"benefit-icon-container mb-4\">
                                        <img src=\"";
        // line 79
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/misc/trust & confidence.png"), "html", null, true);
        yield "\" alt=\"Trust and Confidence\"
                                             class=\"img-fluid\" style=\"max-height: 80px; width: auto;\"
                                             onerror=\"this.style.display='none'; this.nextElementSibling.style.display='flex';\">
                                    </div>
                                    <h4 class=\"fw-bold mb-3 text-white\" style=\"font-family: 'Montserrat', sans-serif;\">Trust and Confidence</h4>
                                    <p class=\"text-white\" style=\"line-height: 1.6; font-family: 'Calibri', Arial, sans-serif;\">
                                        Benefit from transparent reporting and get a dedicated Partner Manager
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<style>
/* Capitol Academy Partnership Page - Brand Standards */
/* Colors: #00233e (blue), #971020 (red), #45403f (gray), white */

/* Remove all section margins for seamless layout */
section {
    margin: 0 !important;
    padding: 0 !important;
}

/* Card Hover Effects */
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(1, 26, 45, 0.3) !important;
    transition: all 0.3s ease;
}

/* Button Hover Effects */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

/* Professional Styling */
.benefit-icon-container img {
    transition: transform 0.3s ease;
}

.card:hover .benefit-icon-container img {
    transform: scale(1.1);
}

/* Hero Section Enhancements */
.hero-section {
    background-size: cover !important;
    background-position: center center !important;
    background-repeat: no-repeat !important;
    background-attachment: fixed !important;
}

.hero-content {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
}

/* Card styling */
.card {
    border-radius: 15px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2.5rem;
    }

    section {
        height: auto !important;
        min-height: 100vh !important;
    }

    .hero-content {
        padding: 2rem !important;
        margin-top: 100px !important;
        width: 90% !important;
    }

    .card-body {
        padding: 2rem !important;
    }
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "home/partnership.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  187 => 79,  167 => 62,  147 => 45,  131 => 32,  103 => 7,  100 => 6,  87 => 5,  64 => 3,  41 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Partnership Program - Capitol Academy{% endblock %}

{% block body %}
    <!-- Section 1: Capitol Academy Partnership Program -->
    <section style=\"background: url('{{ asset('images/backgrounds/Banner CA Partners Prog.png') }}') center/cover; height: 100vh; padding: 0; margin: 0; display: flex; align-items: center;\">
        <div class=\"container h-100\">
            <div class=\"row align-items-center h-100\">
                <div class=\"col-lg-10\">
                    <!-- Larger Glassmorphism Container -->
                    <div class=\"hero-content text-white p-5 rounded-4\" style=\"background: rgba(255, 255, 255, 0.15); backdrop-filter: blur(20px); border: 1px solid rgba(255, 255, 255, 0.25); width: 85%; margin-top: 140px;\">
                        <h1 class=\"display-4 mb-3\" style=\"font-family: 'Montserrat', sans-serif;\">Capitol Academy Partnership Program</h1>
                        <h2 class=\"h2 fw-bold mb-4\" style=\"font-family: 'Montserrat', sans-serif;\">Earn Money by Promoting Us</h2>
                        <p class=\"mb-5\" style=\"font-size: 1.1rem; line-height: 1.6; font-family: 'Calibri', Arial, sans-serif;\">
                            Give your customers the perfect environment to make better decisions in the financial markets.
                        </p>
                        <div class=\"d-flex justify-content-start\">
                            <a href=\"#benefits\" class=\"btn btn-lg px-5 py-3\" style=\"background: #28a745; border: 2px solid white; border-radius: 8px; font-weight: 600; color: white; transition: all 0.3s ease;\"
                               onmouseover=\"this.style.background='#218838'; this.style.transform='translateY(-2px)'\"
                               onmouseout=\"this.style.background='#28a745'; this.style.transform='translateY(0)'\">
                                Join Now
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section 2: Why Partner with Capitol Academy -->
    <section id=\"benefits\" style=\"background: url('{{ asset('images/backgrounds/Bg Why partner with CA.png') }}') center/cover; height: 100vh; margin: 0; padding: 0; display: flex; align-items: center;\">
        <div class=\"container h-100\">
            <div class=\"row h-100 align-items-center\">
                <div class=\"col-12\">
                    <div class=\"text-center mb-5\">
                        <h2 class=\"h1 fw-bold mb-4\" style=\"color: #011a2d; font-family: 'Montserrat', sans-serif;\">Why Partner with Capitol Academy ?</h2>
                    </div>
                    <div class=\"row g-5 justify-content-center\">
                        <!-- Financial Benefits -->
                        <div class=\"col-lg-3 col-md-4\">
                            <div class=\"card border-0 shadow-sm\" style=\"background: linear-gradient(to bottom, #0a1f3c, #2a4d7a); transition: transform 0.3s ease, box-shadow 0.3s ease; min-height: 350px;\">
                                <div class=\"card-body text-center p-5\">
                                    <div class=\"benefit-icon-container mb-4\">
                                        <img src=\"{{ asset('images/misc/Financial_benefits icon.png') }}\" alt=\"Financial Benefits\"
                                             class=\"img-fluid\" style=\"max-height: 80px; width: auto;\"
                                             onerror=\"this.style.display='none'; this.nextElementSibling.style.display='flex';\">
                                    </div>
                                    <h4 class=\"fw-bold mb-3 text-white\" style=\"font-family: 'Montserrat', sans-serif;\">Financial Benefits</h4>
                                    <p class=\"text-white\" style=\"line-height: 1.6; font-family: 'Calibri', Arial, sans-serif;\">
                                        Custom payouts for High-Value Partners & earn CPA from deposits as low as \$1
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Going Global -->
                        <div class=\"col-lg-3 col-md-4\">
                            <div class=\"card border-0 shadow-sm\" style=\"background: linear-gradient(to bottom, #0a1f3c, #2a4d7a); transition: transform 0.3s ease, box-shadow 0.3s ease; min-height: 350px;\">
                                <div class=\"card-body text-center p-5\">
                                    <div class=\"benefit-icon-container mb-4\">
                                        <img src=\"{{ asset('images/misc/Going_global.png') }}\" alt=\"Going Global\"
                                             class=\"img-fluid\" style=\"max-height: 80px; width: auto;\"
                                             onerror=\"this.style.display='none'; this.nextElementSibling.style.display='flex';\">
                                    </div>
                                    <h4 class=\"fw-bold mb-3 text-white\" style=\"font-family: 'Montserrat', sans-serif;\">Going Global</h4>
                                    <p class=\"text-white\" style=\"line-height: 1.6; font-family: 'Calibri', Arial, sans-serif;\">
                                        Benefit from high-converting products and earn from multiple traffic sources.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Trust & Confidence -->
                        <div class=\"col-lg-3 col-md-4\">
                            <div class=\"card border-0 shadow-sm\" style=\"background: linear-gradient(to bottom, #0a1f3c, #2a4d7a); transition: transform 0.3s ease, box-shadow 0.3s ease; min-height: 350px;\">
                                <div class=\"card-body text-center p-5\">
                                    <div class=\"benefit-icon-container mb-4\">
                                        <img src=\"{{ asset('images/misc/trust & confidence.png') }}\" alt=\"Trust and Confidence\"
                                             class=\"img-fluid\" style=\"max-height: 80px; width: auto;\"
                                             onerror=\"this.style.display='none'; this.nextElementSibling.style.display='flex';\">
                                    </div>
                                    <h4 class=\"fw-bold mb-3 text-white\" style=\"font-family: 'Montserrat', sans-serif;\">Trust and Confidence</h4>
                                    <p class=\"text-white\" style=\"line-height: 1.6; font-family: 'Calibri', Arial, sans-serif;\">
                                        Benefit from transparent reporting and get a dedicated Partner Manager
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<style>
/* Capitol Academy Partnership Page - Brand Standards */
/* Colors: #00233e (blue), #971020 (red), #45403f (gray), white */

/* Remove all section margins for seamless layout */
section {
    margin: 0 !important;
    padding: 0 !important;
}

/* Card Hover Effects */
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(1, 26, 45, 0.3) !important;
    transition: all 0.3s ease;
}

/* Button Hover Effects */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

/* Professional Styling */
.benefit-icon-container img {
    transition: transform 0.3s ease;
}

.card:hover .benefit-icon-container img {
    transform: scale(1.1);
}

/* Hero Section Enhancements */
.hero-section {
    background-size: cover !important;
    background-position: center center !important;
    background-repeat: no-repeat !important;
    background-attachment: fixed !important;
}

.hero-content {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
}

/* Card styling */
.card {
    border-radius: 15px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2.5rem;
    }

    section {
        height: auto !important;
        min-height: 100vh !important;
    }

    .hero-content {
        padding: 2rem !important;
        margin-top: 100px !important;
        width: 90% !important;
    }

    .card-body {
        padding: 2rem !important;
    }
}
</style>
{% endblock %}
", "home/partnership.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\home\\partnership.html.twig");
    }
}
