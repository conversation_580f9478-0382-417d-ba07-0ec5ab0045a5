{% extends 'base.html.twig' %}

{% block title %}Partnership Program - Capitol Academy{% endblock %}

{% block body %}
    <!-- Section 1: Hero Section -->
    <section class="hero-section position-relative overflow-hidden" style="background: url('{{ asset('images/backgrounds/Banner CA Partners Prog.png') }}') center center/cover no-repeat; height: calc(100vh - 140px); background-attachment: fixed; margin: 3rem 0;">
        <div class="container h-100">
            <div class="row align-items-center h-100">
                <div class="col-lg-9">
                    <!-- Glassmorphism Container spanning from left margin to 75% width -->
                    <div class="hero-content text-white p-5 rounded-4" style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.2); width: 75%;">
                        <h1 class="display-4 mb-3" style="font-family: 'Montserrat', sans-serif;">Capitol Academy Partnership Program</h1>
                        <h2 class="h2 fw-bold mb-4" style="font-family: 'Montserrat', sans-serif;">Earn Money by Promoting Us</h2>
                        <p class="mb-5" style="font-size: 1.1rem; line-height: 1.6; font-family: 'Calibri', Arial, sans-serif;">
                            Give your customers the perfect environment to make better decisions in the financial markets.
                        </p>
                        <div class="d-flex justify-content-start">
                            <a href="#benefits" class="btn btn-lg px-5 py-3" style="background: #28a745; border: 2px solid white; border-radius: 8px; font-weight: 600; color: white; transition: all 0.3s ease;"
                               onmouseover="this.style.background='#218838'; this.style.transform='translateY(-2px)'"
                               onmouseout="this.style.background='#28a745'; this.style.transform='translateY(0)'">
                                Join Now
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <!-- Section 2: Benefits Section -->
    <section id="benefits" class="py-5" style="background: url('{{ asset('images/backgrounds/Bg Why partner with CA.png') }}') center/cover; height: calc(100vh - 140px); margin: 3rem 0;">
        <div class="container h-100">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <h2 class="h1 fw-bold mb-4" style="color: #011a2d;">Why Partner with Capitol Academy ?</h2>
                </div>
            </div>
            <div class="row g-4">
                <!-- Financial Benefits -->
                <div class="col-lg-4 col-md-6">
                    <div class="card border-0 h-100 shadow-sm" style="background: linear-gradient(to bottom, #0a1f3c, #2a4d7a); transition: transform 0.3s ease, box-shadow 0.3s ease;">
                        <div class="card-body text-center p-4">
                            <div class="benefit-icon-container mb-4">
                                <img src="{{ asset('images/misc/Financial_benefits icon.png') }}" alt="Financial Benefits"
                                     class="img-fluid" style="max-height: 80px; width: auto;"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            </div>
                            <h4 class="fw-bold mb-3 text-white">Financial Benefits</h4>
                            <p class="text-white" style="line-height: 1.6;">
                                Custom payouts for High-Value<br>
                                Partners & earn CPA from<br>
                                deposits as low as $1
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Going Global -->
                <div class="col-lg-4 col-md-6">
                    <div class="card border-0 h-100 shadow-sm" style="background: linear-gradient(to bottom, #0a1f3c, #2a4d7a); transition: transform 0.3s ease, box-shadow 0.3s ease;">
                        <div class="card-body text-center p-4">
                            <div class="benefit-icon-container mb-4">
                                <img src="{{ asset('images/misc/Going_global.png') }}" alt="Going Global"
                                     class="img-fluid" style="max-height: 80px; width: auto;"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            </div>
                            <h4 class="fw-bold mb-3 text-white">Going Global</h4>
                            <p class="text-white" style="line-height: 1.6;">
                                Benefit from high-converting<br>
                                products and earn from multiple<br>
                                traffic sources.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Trust & Confidence -->
                <div class="col-lg-4 col-md-6 mx-auto">
                    <div class="card border-0 h-100 shadow-sm" style="background: linear-gradient(to bottom, #0a1f3c, #2a4d7a); transition: transform 0.3s ease, box-shadow 0.3s ease;">
                        <div class="card-body text-center p-4">
                            <div class="benefit-icon-container mb-4">
                                <img src="{{ asset('images/misc/trust & confidence.png') }}" alt="Trust and Confidence"
                                     class="img-fluid" style="max-height: 80px; width: auto;"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            </div>
                            <h4 class="fw-bold mb-3 text-white">Trust and Confidence</h4>
                            <p class="text-white" style="line-height: 1.6;">
                                Benefit from transparent reporting<br>
                                and get a dedicated Partner<br>
                                Manager
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<style>
/* Capitol Academy Partnership Page - Using Only Specified Colors */
/* Colors: #a90418 (red), #011a2d (dark blue), white, #699901 (green) */

/* Card Hover Effects */
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(1, 26, 45, 0.3) !important;
    transition: all 0.3s ease;
}

/* Button Hover Effects */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(105, 153, 1, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2.5rem;
    }

    .hero-section {
        min-height: 70vh !important;
    }

    .card-body {
        padding: 2rem !important;
    }

    .py-5 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important;
    }

    .hero-content {
        padding: 2rem !important;
    }
}

/* Professional Styling */
.benefit-icon-container img {
    transition: transform 0.3s ease;
}

.card:hover .benefit-icon-container img {
    transform: scale(1.1);
}

/* Hero Section Enhancements */
.hero-section {
    background-size: cover !important;
    background-position: center center !important;
    background-repeat: no-repeat !important;
    background-attachment: fixed !important;
}

.hero-content {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
}

/* Capitol Academy Brand Colors - Only Specified Colors */
.text-primary {
    color: #011a2d !important;
}

.bg-primary {
    background-color: #011a2d !important;
}

.btn-success {
    background: #699901 !important;
    border-color: #699901 !important;
}

.btn-success:hover {
    background: #5a8001 !important;
    border-color: #5a8001 !important;
}

/* Card styling with dark blue background */
.card {
    border-radius: 15px !important;
}

.card:hover {
    border-color: #a90418 !important;
}
</style>
{% endblock %}
